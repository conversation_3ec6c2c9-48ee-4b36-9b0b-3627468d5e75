/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333333;
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 50%, #d0d0d0 100%);
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

/* Animated Background with Circles and Lines */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(150, 150, 150, 0.3) 2px, transparent 2px),
        radial-gradient(circle at 60% 70%, rgba(120, 120, 120, 0.4) 3px, transparent 3px),
        radial-gradient(circle at 80% 20%, rgba(100, 100, 100, 0.2) 1px, transparent 1px),
        radial-gradient(circle at 30% 80%, rgba(140, 140, 140, 0.3) 2px, transparent 2px),
        radial-gradient(circle at 90% 60%, rgba(110, 110, 110, 0.4) 2px, transparent 2px);
    background-repeat: repeat;
    background-size: 150px 150px, 200px 200px, 100px 100px, 180px 180px, 120px 120px;
    animation: moveCircles 25s linear infinite;
    z-index: -2;
}

/* Moving Lines connecting circles */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg, transparent 48%, rgba(130, 130, 130, 0.2) 49%, rgba(130, 130, 130, 0.2) 51%, transparent 52%),
        linear-gradient(-45deg, transparent 48%, rgba(110, 110, 110, 0.15) 49%, rgba(110, 110, 110, 0.15) 51%, transparent 52%),
        linear-gradient(90deg, transparent 98%, rgba(140, 140, 140, 0.1) 100%),
        linear-gradient(0deg, transparent 98%, rgba(120, 120, 120, 0.1) 100%);
    background-size: 80px 80px, 120px 120px, 60px 60px, 100px 100px;
    animation: moveLines 20s linear infinite;
    z-index: -1;
    opacity: 0.6;
}

@keyframes moveCircles {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }
    25% {
        transform: translate(-50px, -30px) rotate(90deg);
    }
    50% {
        transform: translate(-100px, -60px) rotate(180deg);
    }
    75% {
        transform: translate(-150px, -90px) rotate(270deg);
    }
    100% {
        transform: translate(-200px, -120px) rotate(360deg);
    }
}

@keyframes moveLines {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }
    33% {
        transform: translate(30px, 20px) rotate(120deg);
    }
    66% {
        transform: translate(60px, 40px) rotate(240deg);
    }
    100% {
        transform: translate(90px, 60px) rotate(360deg);
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(64, 64, 64, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
    border-bottom: 1px solid #555555;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.2);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    background: linear-gradient(45deg, #000000 0%, #666666 25%, #ffffff 50%, #cccccc 75%, #000000 100%);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: 2px;
    animation: wavyText 3s ease-in-out infinite;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

@keyframes wavyText {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #e0e0e0;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #ffffff;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background: linear-gradient(45deg, #ffffff, #e0e0e0);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f8f8 0%, #e0e0e0 50%, #d8d8d8 100%);
    padding-top: 80px;
    position: relative;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    order: 1;
}

.hero-image {
    order: 2;
}

.hero-title {
    font-size: 5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    line-height: 0.9;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.orca-text {
    color: #000000;
    font-size: 6rem;
    letter-spacing: 8px;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    font-weight: 900;
}

.vision-text {
    color: #000000;
    font-size: 5.5rem;
    letter-spacing: 6px;
    margin-top: -10px;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    font-weight: 900;
}

.hero-subtitle {
    font-size: 1.4rem;
    color: #000000;
    margin-bottom: 1rem;
    line-height: 1.4;
    font-weight: 600;
}

.hero-description {
    font-size: 1rem;
    color: #555555;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    max-width: 500px;
}

.scroll-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 2rem;
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-indicator:hover {
    transform: scale(1.1);
}

.scroll-indicator:hover .scroll-text {
    color: #ffffff;
}

.scroll-indicator:hover .scroll-arrow {
    color: #cccccc;
}

.scroll-text {
    font-size: 0.9rem;
    color: #666666;
    margin-bottom: 0.5rem;
    font-weight: 500;
    letter-spacing: 1px;
}

.scroll-arrow {
    font-size: 1.2rem;
    color: #000000;
    animation: scrollDown 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes scrollDown {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    50% {
        transform: translateY(10px);
        opacity: 0.7;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    border: 2px solid transparent;
}

.btn-primary {
    background: #000000;
    color: #ffffff;
}

.btn-primary:hover {
    background: #333333;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: transparent;
    color: #000000;
    border: 2px solid #000000;
}

.btn-secondary:hover {
    background: #000000;
    color: #ffffff;
    transform: translateY(-2px);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666666;
}

/* About Section */
.about {
    padding: 6rem 0;
    background: linear-gradient(135deg, #e8e8e8 0%, #f0f0f0 50%, #e8e8e8 100%);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text {
    order: 1;
}

.about-image {
    order: 2;
}

.about-item {
    margin-bottom: 2rem;
}

.about-item h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #000000;
    margin-bottom: 1rem;
}

.about-item p {
    color: #555555;
    line-height: 1.6;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 50%, #f5f5f5 100%);
    color: #333;
}

.why-choose-us .section-header h2,
.why-choose-us .section-header p {
    color: #000000;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    transition: transform 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #ddd;
}

.stat-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 3rem;
    color: #000000;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: #666666;
}

/* Services Section */
.services {
    padding: 6rem 0;
    background: linear-gradient(135deg, #e8e8e8 0%, #f0f0f0 50%, #e8e8e8 100%);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.service-card {
    background: rgba(255, 255, 255, 0.9);
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #ddd;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-icon {
    font-size: 3rem;
    color: #000000;
    margin-bottom: 1.5rem;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #000000;
    margin-bottom: 1rem;
}

.service-card p {
    color: #555555;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
}

.service-features li {
    color: #555555;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #000000;
    font-weight: bold;
}

/* Contact Section */
.contact {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 50%, #f5f5f5 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    order: 1;
}

/* Add subtle animation to contact items */
.contact-item:nth-child(1) {
    animation-delay: 0.1s;
}

.contact-item:nth-child(2) {
    animation-delay: 0.2s;
}

.contact-item:nth-child(3) {
    animation-delay: 0.3s;
}

/* Add a subtle glow effect */
.contact-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.05) 100%);
    border-radius: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.contact-item:hover::after {
    opacity: 1;
}

.contact-form {
    order: 2;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 240, 240, 0.8) 100%);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #000000 0%, #666666 50%, #000000 100%);
    transition: width 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.contact-item:hover::before {
    width: 8px;
}

.contact-icon {
    font-size: 1.8rem;
    color: #000000;
    margin-right: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
    padding: 12px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    min-height: 50px;
    transition: all 0.3s ease;
}

.contact-item:hover .contact-icon {
    background: linear-gradient(135deg, #000000 0%, #333333 100%);
    color: #ffffff;
    transform: scale(1.1);
}

.contact-details h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #000000;
    margin-bottom: 0.5rem;
    letter-spacing: 0.5px;
}

.contact-details p {
    color: #555555;
    line-height: 1.6;
    font-weight: 500;
}

.contact-form {
    background: rgba(255, 255, 255, 0.9);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #ddd;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #cccccc;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: #ffffff;
    color: #333333;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #000000;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #e0e0e0 0%, #d0d0d0 50%, #e0e0e0 100%);
    color: #333;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #000000;
}

.footer-section p {
    color: #555555;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #555555;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #000000;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    color: #000000;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #000000;
    color: #ffffff;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    color: #555555;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(64, 64, 64, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.2);
        padding: 2rem 0;
        backdrop-filter: blur(10px);
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }
    
    .orca-text {
        font-size: 3.5rem;
        letter-spacing: 4px;
    }
    
    .vision-text {
        font-size: 3rem;
        letter-spacing: 3px;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .hero-description {
        font-size: 0.9rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .orca-text {
        font-size: 2.8rem;
        letter-spacing: 2px;
    }
    
    .vision-text {
        font-size: 2.4rem;
        letter-spacing: 2px;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-description {
        font-size: 0.85rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .contact-form {
        padding: 2rem;
    }
}

/* Additional Circle Animations */
@keyframes floatingCircles {
    0% {
        transform: translateY(0px) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0px) scale(1);
        opacity: 0.6;
    }
}

@keyframes pulseCircles {
    0% {
        transform: scale(1);
        opacity: 0.4;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 0.4;
    }
}

/* Add floating circles overlay */
.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 15% 25%, rgba(100, 100, 100, 0.3) 4px, transparent 4px),
        radial-gradient(circle at 85% 75%, rgba(120, 120, 120, 0.4) 6px, transparent 6px),
        radial-gradient(circle at 70% 30%, rgba(90, 90, 90, 0.2) 3px, transparent 3px),
        radial-gradient(circle at 25% 80%, rgba(110, 110, 110, 0.3) 5px, transparent 5px);
    background-size: 300px 300px, 250px 250px, 200px 200px, 350px 350px;
    animation: floatingCircles 8s ease-in-out infinite;
    z-index: 1;
    pointer-events: none;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 60% 20%, rgba(130, 130, 130, 0.2) 2px, transparent 2px),
        radial-gradient(circle at 40% 90%, rgba(140, 140, 140, 0.3) 4px, transparent 4px),
        radial-gradient(circle at 90% 40%, rgba(100, 100, 100, 0.25) 3px, transparent 3px);
    background-size: 180px 180px, 220px 220px, 160px 160px;
    animation: pulseCircles 6s ease-in-out infinite;
    z-index: 1;
    pointer-events: none;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}
