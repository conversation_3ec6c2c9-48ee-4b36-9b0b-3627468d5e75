/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background-color: #1a1a1a;
    overflow-x: hidden;
    position: relative;
}

/* Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(2px 2px at 20px 30px, #333, transparent),
        radial-gradient(2px 2px at 40px 70px, #444, transparent),
        radial-gradient(1px 1px at 90px 40px, #555, transparent),
        radial-gradient(1px 1px at 130px 80px, #333, transparent),
        radial-gradient(2px 2px at 160px 30px, #444, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: moveBackground 20s linear infinite;
    z-index: -1;
}

/* Moving Lines */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(90deg, transparent 98%, #333 100%),
        linear-gradient(180deg, transparent 98%, #444 100%);
    background-size: 50px 50px;
    animation: moveLines 15s linear infinite;
    z-index: -1;
    opacity: 0.3;
}

@keyframes moveBackground {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(-200px, -100px);
    }
}

@keyframes moveLines {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(50px, 50px);
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
    border-bottom: 1px solid #333333;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #cccccc;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #ffffff;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background-color: #ffffff;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    padding-top: 80px;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content {
    order: 1;
}

.hero-image {
    order: 2;
}

.hero-title {
    font-size: 5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    line-height: 0.9;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.orca-text {
    background: linear-gradient(135deg, #ffffff 0%, #cccccc 50%, #999999 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 6rem;
    letter-spacing: 8px;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.1);
}

.vision-text {
    background: linear-gradient(135deg, #999999 0%, #cccccc 50%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 5.5rem;
    letter-spacing: 6px;
    margin-top: -10px;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.1);
}

.hero-subtitle {
    font-size: 1.4rem;
    color: #cccccc;
    margin-bottom: 1rem;
    line-height: 1.4;
    font-weight: 500;
}

.hero-description {
    font-size: 1rem;
    color: #999999;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    max-width: 500px;
}

.scroll-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 2rem;
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-indicator:hover {
    transform: scale(1.1);
}

.scroll-indicator:hover .scroll-text {
    color: #ffffff;
}

.scroll-indicator:hover .scroll-arrow {
    color: #cccccc;
}

.scroll-text {
    font-size: 0.9rem;
    color: #999999;
    margin-bottom: 0.5rem;
    font-weight: 500;
    letter-spacing: 1px;
}

.scroll-arrow {
    font-size: 1.2rem;
    color: #ffffff;
    animation: scrollDown 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes scrollDown {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    50% {
        transform: translateY(10px);
        opacity: 0.7;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    border: 2px solid transparent;
}

.btn-primary {
    background: #ffffff;
    color: #1a1a1a;
}

.btn-primary:hover {
    background: #cccccc;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 255, 255, 0.1);
}

.btn-secondary {
    background: transparent;
    color: #ffffff;
    border: 2px solid #ffffff;
}

.btn-secondary:hover {
    background: #ffffff;
    color: #1a1a1a;
    transform: translateY(-2px);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(255, 255, 255, 0.1);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #999999;
}

/* About Section */
.about {
    padding: 6rem 0;
    background: #2a2a2a;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text {
    order: 1;
}

.about-image {
    order: 2;
}

.about-item {
    margin-bottom: 2rem;
}

.about-item h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1rem;
}

.about-item p {
    color: #cccccc;
    line-height: 1.6;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(255, 255, 255, 0.1);
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 6rem 0;
    background: #1a1a1a;
    color: #fff;
}

.why-choose-us .section-header h2,
.why-choose-us .section-header p {
    color: #fff;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-10px);
}

.stat-icon {
    font-size: 3rem;
    color: #fff;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: #ccc;
}

/* Services Section */
.services {
    padding: 6rem 0;
    background: #2a2a2a;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.service-card {
    background: #333333;
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #444444;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(255, 255, 255, 0.15);
}

.service-icon {
    font-size: 3rem;
    color: #ffffff;
    margin-bottom: 1.5rem;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1rem;
}

.service-card p {
    color: #cccccc;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
}

.service-features li {
    color: #cccccc;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #ffffff;
    font-weight: bold;
}

/* Contact Section */
.contact {
    padding: 6rem 0;
    background: #1a1a1a;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    order: 1;
}

.contact-form {
    order: 2;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.contact-icon {
    font-size: 1.5rem;
    color: #ffffff;
    margin-right: 1rem;
    margin-top: 0.5rem;
}

.contact-details h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: #cccccc;
    line-height: 1.6;
}

.contact-form {
    background: #333333;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #444444;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: #2a2a2a;
    color: #ffffff;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ffffff;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: #fff;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #fff;
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #fff;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    color: #fff;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #ffffff;
    color: #1a1a1a;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: #1a1a1a;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(255, 255, 255, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }
    
    .orca-text {
        font-size: 3.5rem;
        letter-spacing: 4px;
    }
    
    .vision-text {
        font-size: 3rem;
        letter-spacing: 3px;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .hero-description {
        font-size: 0.9rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .orca-text {
        font-size: 2.8rem;
        letter-spacing: 2px;
    }
    
    .vision-text {
        font-size: 2.4rem;
        letter-spacing: 2px;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-description {
        font-size: 0.85rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .contact-form {
        padding: 2rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}
