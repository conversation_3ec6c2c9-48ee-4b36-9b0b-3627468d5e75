/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    background: linear-gradient(135deg, #96A3AB 0%, #76AABF 25%, #678391 50%, #056174 75%, #034153 100%);
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

/* Ocean Waves Animation Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse at center, rgba(150, 163, 171, 0.15) 0%, transparent 50%),
        linear-gradient(90deg, transparent 0%, rgba(118, 170, 191, 0.2) 25%, rgba(150, 163, 171, 0.25) 50%, rgba(118, 170, 191, 0.2) 75%, transparent 100%);
    background-size: 300px 300px, 800px 200px;
    animation: oceanWaves 15s ease-in-out infinite;
    z-index: -2;
}

/* Wave Patterns */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        repeating-linear-gradient(
            0deg,
            transparent,
            transparent 20px,
            rgba(150, 163, 171, 0.08) 20px,
            rgba(150, 163, 171, 0.08) 40px
        ),
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 30px,
            rgba(118, 170, 191, 0.1) 30px,
            rgba(118, 170, 191, 0.1) 60px
        );
    animation: waveFlow 20s linear infinite;
    z-index: -1;
    opacity: 0.5;
}

@keyframes oceanWaves {
    0% {
        transform: translateX(0) translateY(0) scale(1);
        background-position: 0% 0%, 0% 50%;
    }
    25% {
        transform: translateX(-20px) translateY(-10px) scale(1.05);
        background-position: 25% 25%, 25% 75%;
    }
    50% {
        transform: translateX(-40px) translateY(-20px) scale(1.1);
        background-position: 50% 50%, 50% 100%;
    }
    75% {
        transform: translateX(-60px) translateY(-10px) scale(1.05);
        background-position: 75% 25%, 75% 75%;
    }
    100% {
        transform: translateX(-80px) translateY(0) scale(1);
        background-position: 100% 0%, 100% 50%;
    }
}

@keyframes waveFlow {
    0% {
        transform: translateX(0) translateY(0);
        opacity: 0.7;
    }
    50% {
        transform: translateX(-100px) translateY(-20px);
        opacity: 0.5;
    }
    100% {
        transform: translateX(-200px) translateY(0);
        opacity: 0.7;
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(135deg, rgba(26, 54, 93, 0.95) 0%, rgba(44, 82, 130, 0.95) 50%, rgba(56, 178, 172, 0.95) 100%);
    backdrop-filter: blur(15px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(56, 178, 172, 0.3);
    box-shadow: 0 4px 25px rgba(56, 178, 172, 0.2);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-image {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(56, 178, 172, 0.8);
    box-shadow: 0 4px 15px rgba(56, 178, 172, 0.3);
    transition: all 0.3s ease;
}

.logo-image:hover {
    transform: scale(1.1) rotate(5deg);
    border-color: rgba(77, 209, 199, 1);
    box-shadow: 0 6px 20px rgba(77, 209, 199, 0.5);
}

.nav-logo h2 {
    background: linear-gradient(45deg, #1a365d 0%, #2c5282 25%, #3182ce 50%, #4299e1 75%, #63b3ed 100%);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: 2px;
    animation: wavyText 3s ease-in-out infinite;
    text-shadow: 0 0 10px rgba(56, 178, 172, 0.3);
}

@keyframes wavyText {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: rgba(230, 255, 250, 0.9);
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(129, 230, 217, 0.5);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background: linear-gradient(45deg, #81e6d9, #4fd1c7, #38b2ac);
    transition: width 0.3s ease;
    border-radius: 2px;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #96A3AB 0%, #76AABF 25%, #678391 50%, #056174 75%, #034153 100%);
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    order: 1;
}

.hero-image {
    order: 2;
}

.hero-title {
    font-size: 5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    line-height: 0.9;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.orca-text {
    background: linear-gradient(45deg, #034153 0%, #056174 25%, #678391 50%, #76AABF 75%, #96A3AB 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 6rem;
    letter-spacing: 8px;
    text-shadow: 2px 2px 8px rgba(3, 65, 83, 0.3);
    position: relative;
    font-weight: 900;
}

.vision-text {
    background: linear-gradient(-45deg, #056174 0%, #678391 25%, #76AABF 50%, #96A3AB 75%, #76AABF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 5.5rem;
    letter-spacing: 6px;
    margin-top: -10px;
    text-shadow: 2px 2px 8px rgba(3, 65, 83, 0.3);
    position: relative;
    font-weight: 900;
}

.hero-subtitle {
    font-size: 1.4rem;
    color: #1a1a1a;
    margin-bottom: 1rem;
    line-height: 1.4;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.5);
}

.hero-description {
    font-size: 1rem;
    color: #2d2d2d;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    max-width: 500px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.scroll-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 2rem;
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-indicator:hover {
    transform: scale(1.1);
}

.scroll-indicator:hover .scroll-text {
    color: #ffffff;
}

.scroll-indicator:hover .scroll-arrow {
    color: #cccccc;
}

.scroll-text {
    font-size: 0.9rem;
    color: #666666;
    margin-bottom: 0.5rem;
    font-weight: 500;
    letter-spacing: 1px;
}

.scroll-arrow {
    font-size: 1.2rem;
    color: #000000;
    animation: scrollDown 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes scrollDown {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    50% {
        transform: translateY(10px);
        opacity: 0.7;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    border: 2px solid transparent;
}

.btn-primary {
    background: linear-gradient(135deg, #056174 0%, #678391 50%, #76AABF 100%);
    color: #ffffff;
    border: none;
    box-shadow: 0 4px 15px rgba(5, 97, 116, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #034153 0%, #056174 50%, #678391 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(5, 97, 116, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #1a1a1a;
    border: 2px solid #056174;
    box-shadow: 0 4px 15px rgba(5, 97, 116, 0.2);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #056174 0%, #678391 100%);
    color: #ffffff;
    transform: translateY(-3px);
    border-color: #678391;
    box-shadow: 0 8px 25px rgba(103, 131, 145, 0.4);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #034153 0%, #056174 50%, #678391 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(3, 65, 83, 0.2);
}

.section-header p {
    font-size: 1.1rem;
    color: #2d2d2d;
}

/* About Section */
.about {
    padding: 6rem 0;
    background: linear-gradient(135deg, #76AABF 0%, #678391 25%, #056174 50%, #034153 75%, #056174 100%);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text {
    order: 1;
}

.about-image {
    order: 2;
}

.about-item {
    margin-bottom: 2rem;
}

.about-item h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.5);
}

.about-item p {
    color: #2d2d2d;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 6rem 0;
    background: linear-gradient(135deg, #96A3AB 0%, #76AABF 25%, #678391 50%, #056174 75%, #034153 100%);
    color: #1a1a1a;
}

.why-choose-us .section-header h2 {
    background: linear-gradient(45deg, #034153 0%, #056174 50%, #678391 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.why-choose-us .section-header p {
    color: #2d2d2d;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(150, 163, 171, 0.1) 100%);
    border-radius: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(5, 97, 116, 0.2);
    border: 1px solid rgba(5, 97, 116, 0.3);
}

.stat-item:hover {
    transform: translateY(-15px) scale(1.05);
    box-shadow: 0 25px 50px rgba(5, 97, 116, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(118, 170, 191, 0.2) 100%);
}

.stat-icon {
    font-size: 3rem;
    background: linear-gradient(45deg, #056174 0%, #678391 50%, #76AABF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #034153 0%, #056174 50%, #678391 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: #2d2d2d;
}

/* Services Section */
.services {
    padding: 6rem 0;
    background: linear-gradient(135deg, #76AABF 0%, #678391 25%, #056174 50%, #034153 75%, #056174 100%);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.service-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(150, 163, 171, 0.1) 100%);
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(5, 97, 116, 0.2);
    transition: all 0.3s ease;
    border: 1px solid rgba(5, 97, 116, 0.3);
    backdrop-filter: blur(10px);
}

.service-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 50px rgba(5, 97, 116, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(118, 170, 191, 0.2) 100%);
}

.service-icon {
    font-size: 3rem;
    background: linear-gradient(45deg, #056174 0%, #678391 50%, #76AABF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    background: linear-gradient(45deg, #034153 0%, #056174 50%, #678391 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.service-card p {
    color: #2d2d2d;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
}

.service-features li {
    color: #2d2d2d;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    background: linear-gradient(45deg, #056174 0%, #678391 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* Contact Section */
.contact {
    padding: 6rem 0;
    background: linear-gradient(135deg, #96A3AB 0%, #76AABF 25%, #678391 50%, #056174 75%, #034153 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    order: 1;
}

/* Add subtle animation to contact items */
.contact-item:nth-child(1) {
    animation-delay: 0.1s;
}

.contact-item:nth-child(2) {
    animation-delay: 0.2s;
}

.contact-item:nth-child(3) {
    animation-delay: 0.3s;
}

/* Add a subtle glow effect */
.contact-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.05) 100%);
    border-radius: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.contact-item:hover::after {
    opacity: 1;
}

.contact-form {
    order: 2;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 240, 240, 0.8) 100%);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #000000 0%, #666666 50%, #000000 100%);
    transition: width 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.contact-item:hover::before {
    width: 8px;
}

.contact-icon {
    font-size: 1.8rem;
    background: linear-gradient(45deg, #056174 0%, #678391 50%, #76AABF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-right: 1.5rem;
    background-color: linear-gradient(135deg, rgba(5, 97, 116, 0.1) 0%, rgba(103, 131, 145, 0.05) 100%);
    padding: 12px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    min-height: 50px;
    transition: all 0.3s ease;
    border: 2px solid rgba(5, 97, 116, 0.3);
}

.contact-item:hover .contact-icon {
    background: linear-gradient(135deg, #056174 0%, #678391 100%);
    color: #ffffff;
    transform: scale(1.1);
    border-color: #678391;
    box-shadow: 0 5px 15px rgba(103, 131, 145, 0.4);
}

.contact-details h4 {
    font-size: 1.2rem;
    font-weight: 600;
    background: linear-gradient(45deg, #034153 0%, #056174 50%, #678391 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    letter-spacing: 0.5px;
}

.contact-details p {
    color: #2d2d2d;
    line-height: 1.6;
    font-weight: 500;
}

.contact-form {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(150, 163, 171, 0.1) 100%);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(5, 97, 116, 0.2);
    border: 1px solid rgba(5, 97, 116, 0.3);
    backdrop-filter: blur(10px);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid rgba(5, 97, 116, 0.3);
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1a1a1a;
    backdrop-filter: blur(5px);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #056174;
    box-shadow: 0 0 15px rgba(5, 97, 116, 0.3);
    background: rgba(255, 255, 255, 1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #034153 0%, #056174 25%, #678391 50%, #76AABF 75%, #96A3AB 100%);
    color: #96A3AB;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.footer-section p {
    color: #76AABF;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #76AABF;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(118, 170, 191, 0.5);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(5, 97, 116, 0.3) 0%, rgba(103, 131, 145, 0.3) 100%);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    color: #ffffff;
    transition: all 0.3s ease;
    border: 1px solid rgba(118, 170, 191, 0.3);
}

.social-links a:hover {
    background: linear-gradient(135deg, #056174 0%, #678391 100%);
    color: #ffffff;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(103, 131, 145, 0.4);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(118, 170, 191, 0.3);
    color: #76AABF;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(64, 64, 64, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.2);
        padding: 2rem 0;
        backdrop-filter: blur(10px);
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }
    
    .orca-text {
        font-size: 3.5rem;
        letter-spacing: 4px;
    }
    
    .vision-text {
        font-size: 3rem;
        letter-spacing: 3px;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .hero-description {
        font-size: 0.9rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .orca-text {
        font-size: 2.8rem;
        letter-spacing: 2px;
    }
    
    .vision-text {
        font-size: 2.4rem;
        letter-spacing: 2px;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-description {
        font-size: 0.85rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .contact-form {
        padding: 2rem;
    }
}

/* Additional Circle Animations */
@keyframes floatingCircles {
    0% {
        transform: translateY(0px) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0px) scale(1);
        opacity: 0.6;
    }
}

@keyframes pulseCircles {
    0% {
        transform: scale(1);
        opacity: 0.4;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 0.4;
    }
}

/* Add floating circles overlay */
.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 15% 25%, rgba(100, 100, 100, 0.3) 4px, transparent 4px),
        radial-gradient(circle at 85% 75%, rgba(120, 120, 120, 0.4) 6px, transparent 6px),
        radial-gradient(circle at 70% 30%, rgba(90, 90, 90, 0.2) 3px, transparent 3px),
        radial-gradient(circle at 25% 80%, rgba(110, 110, 110, 0.3) 5px, transparent 5px);
    background-size: 300px 300px, 250px 250px, 200px 200px, 350px 350px;
    animation: floatingCircles 8s ease-in-out infinite;
    z-index: 1;
    pointer-events: none;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 60% 20%, rgba(130, 130, 130, 0.2) 2px, transparent 2px),
        radial-gradient(circle at 40% 90%, rgba(140, 140, 140, 0.3) 4px, transparent 4px),
        radial-gradient(circle at 90% 40%, rgba(100, 100, 100, 0.25) 3px, transparent 3px);
    background-size: 180px 180px, 220px 220px, 160px 160px;
    animation: pulseCircles 6s ease-in-out infinite;
    z-index: 1;
    pointer-events: none;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}
